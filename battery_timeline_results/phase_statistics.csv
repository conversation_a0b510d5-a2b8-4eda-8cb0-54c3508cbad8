phase,metric,value
phase1,raw_repair_records,13080
phase1,raw_vehicle_records,26693
phase1,clean_events,13080
phase1,unique_vins,26800
phase1,data_quality_score,0.29
phase2,clear_events_processed,9160
phase2,simple_periods_created,18314
phase2,vins_with_clear_data,7004
phase2,average_periods_per_vin,2.614791547687036
phase3,periods_before_enhancement,18314
phase3,periods_after_enhancement,27969
phase3,new_periods_inferred,9655
phase3,inference_applications,1446
phase3,confidence_upgrades,0
phase4,orphaned_events_processed,3060
phase4,conflicts_resolved,3289
phase4,inconsistencies_handled,0
phase4,transfers_validated,6898
phase4,periods_after_edge_handling,25377
phase5,validation_issues_fixed,7
phase5,duplicates_removed,0
phase5,lifecycle_stages_assigned,25377
phase5,final_timeline_size,25377
phase5,overall_quality_score,0.85
phase5,quality_metrics,"{'overall_score': 0.85, 'confidence_distribution': {'high': 9171, 'medium': 16172, 'low': 34}, 'source_distribution': {'repair_event': 16124, 'snapshot_only': 9253}, 'completeness': {'complete': 25377, 'active': 0, 'incomplete': 0}}"
