
SIMPLE BATTERY TIMELINE REPORT
==================================================

OVERVIEW:
- Analysis Date: 2025-07-11 01:29:12
- HV Repair File: hv_repair_2025-06-02b.csv
- Working Vehicles Files: comparison_results/working_matching_vehicles.csv and comparison_results/working_unique_vehicles.csv
- Total Raw HV Records: 13,080
- Total Working Vehicle Records: 26,693

TIMELINE SUMMARY:
- Total Timeline Periods: 19,143
- Unique Batteries: 11,042
- Unique Vehicles: 8,885
- Active Periods: 6,783
- Completed Periods: 12,360

BATTERY MOBILITY:
- Single-Vehicle Batteries: 5,025
- Multi-Vehicle Batteries: 6,017

DATE RANGE:
- From: 2020-08-27 00:00:00
- To: 2025-05-30 00:00:00
- Span: 1737 days

TIMELINE FORMAT:
Each row represents: battery_id | vin | start_date | end_date

Examples:
  100 | WF0JXXTTGJHK10604 | 2020-10-13 | 2024-02-09
  100 | WF0JXXTTGJHG48865 | 2024-03-18 | Active
  10001 | WS5D16BDAHA100196 | Unknown | 2021-07-13
  10001 | WS5D16BDAHA100679 | 2021-07-22 | 2022-11-23
  10024 | WS5D16BDAGA100013 | Unknown | 2021-03-05

NEXT STEPS:
1. Use the exported CSV for km calculations
2. Join with vehicle data using VIN
3. Join with odometer data using date ranges
4. Calculate km per battery period
5. Sum km across all periods for total battery km

✅ Simple timeline ready for km calculations!
